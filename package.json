{"name": "js-booster", "version": "1.1.1", "description": "JS utility library with virtual scrolling | JS工具库，包含虚拟滚动", "type": "module", "main": "dist/js-booster.js", "module": "dist/js-booster.esm.js", "files": ["src", "dist", "README.md", "LICENSE"], "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "build": "rollup -c", "prepublishOnly": "npm run build"}, "keywords": ["virtual scroll", "performance", "frontend", "ui components", "javascript"], "author": "cg-zhou", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cg-zhou/js-booster.git"}, "bugs": {"url": "https://github.com/cg-zhou/js-booster/issues"}, "homepage": "https://github.com/cg-zhou/js-booster#readme", "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "rollup": "^4.9.1"}}